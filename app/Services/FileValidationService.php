<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class FileValidationService
{
    /**
     * Get allowed file extensions from config
     *
     * @return array
     */
    public static function getAllowedExtensions(): array
    {
        $config = config('file_validation.allowed_extensions', []);
        return array_merge(
            $config['images'] ?? [],
            $config['documents'] ?? [],
            $config['archives'] ?? [],
            $config['videos'] ?? [],
            $config['audio'] ?? []
        );
    }

    /**
     * Get dangerous file extensions from config
     *
     * @return array
     */
    public static function getDangerousExtensions(): array
    {
        return config('file_validation.dangerous_extensions', []);
    }

    /**
     * Get allowed MIME types from config
     *
     * @return array
     */
    public static function getAllowedMimeTypes(): array
    {
        return config('file_validation.allowed_mime_types', []);
    }

    /**
     * Get dangerous MIME types from config
     *
     * @return array
     */
    public static function getDangerousMimeTypes(): array
    {
        return config('file_validation.dangerous_mime_types', []);
    }

    /**
     * Get maximum file size from config
     *
     * @param string $type
     * @return int
     */
    public static function getMaxFileSize(string $type = 'default'): int
    {
        return config("file_validation.max_sizes.{$type}", config('file_validation.max_sizes.default', 50 * 1024 * 1024));
    }

    /**
     * Validate an uploaded file for security
     *
     * @param UploadedFile $file
     * @param array $options
     * @return void
     * @throws ValidationException
     */
    public static function validateFile(UploadedFile $file, array $options = []): void
    {
        $maxSize = $options['max_size'] ?? self::getMaxFileSize();
        $allowedExtensions = $options['allowed_extensions'] ?? self::getAllowedExtensions();
        $allowedMimeTypes = $options['allowed_mime_types'] ?? self::getAllowedMimeTypes();

        // Check file extension
        $extension = strtolower($file->getClientOriginalExtension());

        if (in_array($extension, self::getDangerousExtensions())) {
            throw ValidationException::withMessages([
                'file' => [config('file_validation.error_messages.dangerous_extension', 'The uploaded file type is not allowed for security reasons.')]
            ]);
        }

        if (!in_array($extension, $allowedExtensions)) {
            throw ValidationException::withMessages([
                'file' => [config('file_validation.error_messages.invalid_extension', 'The uploaded file extension is not allowed.')]
            ]);
        }

        // Check file size
        if ($file->getSize() > $maxSize) {
            $maxSizeMB = round($maxSize / (1024 * 1024), 2);
            $message = str_replace(':max_size', "{$maxSizeMB}MB", config('file_validation.error_messages.file_too_large', 'The uploaded file is too large. Maximum size is :max_size.'));
            throw ValidationException::withMessages([
                'file' => [$message]
            ]);
        }

        // Check MIME type
        $mimeType = $file->getMimeType();

        if (in_array($mimeType, self::getDangerousMimeTypes())) {
            throw ValidationException::withMessages([
                'file' => [config('file_validation.error_messages.dangerous_mime', 'The uploaded file MIME type is not allowed for security reasons.')]
            ]);
        }

        if (!in_array($mimeType, $allowedMimeTypes)) {
            throw ValidationException::withMessages([
                'file' => [config('file_validation.error_messages.invalid_mime', 'The uploaded file MIME type is not allowed.')]
            ]);
        }

        // Additional security checks
        self::performAdditionalSecurityChecks($file);
    }

    /**
     * Validate a file for Trix editor uploads
     *
     * @param UploadedFile $file
     * @return void
     * @throws ValidationException
     */
    public static function validateTrixFile(UploadedFile $file): void
    {
        self::validateFile($file, [
            'max_size' => self::getMaxFileSize('trix'),
        ]);
    }

    /**
     * Perform additional security checks on the uploaded file
     *
     * @param UploadedFile $file
     * @return void
     * @throws ValidationException
     */
    private static function performAdditionalSecurityChecks(UploadedFile $file): void
    {
        // Check for 0-byte files
        if ($file->getSize() === 0) {
            throw ValidationException::withMessages([
                'file' => ['Empty files (0 bytes) are not allowed for security reasons.']
            ]);
        }

        // Check for null bytes in filename (potential directory traversal)
        if (strpos($file->getClientOriginalName(), "\0") !== false) {
            throw ValidationException::withMessages([
                'file' => ['Invalid file name detected.']
            ]);
        }

        // Check for directory traversal patterns
        $filename = $file->getClientOriginalName();
        if (strpos($filename, '../') !== false || strpos($filename, '..\\') !== false) {
            throw ValidationException::withMessages([
                'file' => ['Invalid file name detected.']
            ]);
        }

        // Check file content for PHP tags (basic check)
        if (in_array(strtolower($file->getClientOriginalExtension()), ['txt', 'csv', 'rtf'])) {
            $content = file_get_contents($file->getRealPath());
            if (strpos($content, '<?php') !== false || strpos($content, '<?=') !== false) {
                throw ValidationException::withMessages([
                    'file' => ['Suspicious file content detected.']
                ]);
            }
        }
    }

    /**
     * Get validation rules for file uploads
     *
     * @param array $options
     * @return array
     */
    public static function getValidationRules(array $options = []): array
    {
        $maxSize = $options['max_size_kb'] ?? (self::getMaxFileSize() / 1024);
        $allowedExtensions = $options['allowed_extensions'] ?? self::getAllowedExtensions();

        return [
            'required',
            'file',
            'mimes:' . implode(',', $allowedExtensions),
            'max:' . $maxSize,
        ];
    }

    /**
     * Get validation rules for Trix file uploads
     *
     * @return array
     */
    public static function getTrixValidationRules(): array
    {
        return self::getValidationRules([
            'max_size_kb' => self::getMaxFileSize('trix') / 1024
        ]);
    }

    /**
     * Generate a secure filename using UUID and safe extension
     *
     * @param UploadedFile $file
     * @return string
     */
    public static function generateSecureFilename(UploadedFile $file): string
    {
        // Get the last extension (handles cases like file.php.jpg)
        $extension = $file->getClientOriginalExtension();

        // Generate a new, random, and safe filename
        $safeFilename = Str::uuid() . '.' . $extension;

        return $safeFilename;
    }
}
