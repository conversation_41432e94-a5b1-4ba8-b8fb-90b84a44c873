<?php

namespace App\Nova;

use App\Nova\Actions\ActivateArticle;
use App\Nova\Metrics\articles\Myunvalidatedarticle;
use App\Nova\Metrics\articles\Myvalidatedarticle;
use App\Nova\Metrics\articles\Unvalidatedarticle;
use App\Nova\Metrics\articles\Validatedarticle;
use Auth;
use Illuminate\Http\Request;
use Laravel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\Boolean;
use Laravel\Nova\Fields\Hidden;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Fields\Number;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Http\Requests\NovaRequest;
use Allanvb\NovaExports\ExportResourceAction;
use Allanvb\NovaExports\ExportxlsxResourceAction;
use Illuminate\Support\Str;

class InterConference extends Resource
{
    public static function label()
    {
        return __('المؤتمرات الدولیة');
    }

    /**
     * Get the displayable singular label of the resource.
     *
     * @return string
     */
    public static function singularLabel()
    {
        return __('المؤتمرات الدولیة');
    }

    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\Models\InterConference::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'title';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'title', 'code', 'conference_title', 'author',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function fields(Request $request)
    {

        return [
            ID::make(__('#'), 'id')
                ->sortable()
                ->hideFromIndex(),

            Text::make(__('رقم'), 'code')
                ->rules('required', 'string', 'max:50', 'unique:inter_conferences,code,{{resourceId}}'),

            BelongsTo::make(__('عنوان المصدر'), 'sourcegroups', Sourcegroup::class)
                ->displayUsing(function ($dat) {
                    return Str::substr($dat->sourcetitle, 0, 250);
                })
                ->sortable()
                ->onlyOnIndex(),

            BelongsTo::make(__('عنوان المصدر'), 'sourcegroups', Sourcegroup::class)
                ->sortable()
                ->hideFromIndex()
                ->nullable()
                ->placeholder(__('اختر عنوان المصدر'))
                ->rules('nullable', 'exists:sourcegroups,id'),

            Text::make(__('الجهة المنظمة للمؤتمر'), 'organiser')
                ->displayUsing(function($dat) { 
                    return Str::substr($dat, 0, 20) . "...";
                })
                ->rules('required', 'string', 'max:300')
                ->onlyOnIndex(),

            Text::make(__('الجهة المنظمة للمؤتمر'), 'organiser')
                ->rules('required', 'string', 'max:300')
                ->hideFromIndex(),

            Text::make(__('عنوان المؤتمر'), 'conference_title')
                ->displayUsing(function($dat) { 
                    return Str::substr($dat, 0, 20) . "...";
                })
                ->rules('required', 'string', 'max:500')
                ->onlyOnIndex(),

            Text::make(__('عنوان المؤتمر'), 'conference_title')
                ->rules('required', 'string', 'max:500')
                ->hideFromIndex(),

            Text::make(__('سنة الانعقاد'), 'year')->rules('nullable', 'string', 'max:300'),

            Text::make(__('رقم الجلسة'), 'session_number')->rules('nullable', 'string', 'max:300'),

            Text::make(__('عنوان الجلسة'), 'session_title')
                ->displayUsing(function($dat) { 
                    return Str::substr($dat, 0, 20) . "...";
                })
                ->rules('nullable', 'string', 'max:300')
                ->onlyOnIndex(),

            Text::make(__('عنوان الجلسة'), 'session_title')
                ->rules('nullable', 'string', 'max:300')
                ->hideFromIndex(),

            Text::make(__('عنوان البحث'), 'research_number')->rules('nullable', 'string', 'max:300'),

            Text::make(__('العنوان'), 'title')
                ->displayUsing(function($dat) { 
                    return Str::substr($dat, 0, 20) . "...";
                })
                ->rules('required', 'string', 'max:500')
                ->onlyOnIndex(),

            Text::make(__('العنوان'), 'title')
                ->rules('required', 'string', 'max:500')
                ->hideFromIndex(),

            Text::make(__('اسم الباحث'), 'author')
                ->displayUsing(function($dat) { 
                    return Str::substr($dat, 0, 20) . "...";
                })
                ->rules('required', 'string', 'max:255')
                ->onlyOnIndex(),

            Text::make(__('اسم الباحث'), 'author')
                ->rules('required', 'string', 'max:255')
                ->hideFromIndex(),

            Text::make(__('رقم الفصل'), 'chapter_number')->rules('nullable', 'string', 'max:300'),

            Text::make(__('عنوان الفصل'), 'chapter_title')
                ->rules('nullable', 'string', 'max:300')
                ->hideFromIndex(),

            Text::make(__('رقم المبحث'), 'search_number')->rules('nullable', 'string', 'max:300'),

            Text::make(__('عنوان المبحث'), 'search_title')
                ->rules('nullable', 'string', 'max:300')
                ->hideFromIndex(),

            Text::make(__('رقم المطلب'), 'request_number')->rules('nullable', 'string', 'max:300'),

            Text::make(__('عنوان المطلب'), 'request_title')
                ->rules('nullable', 'string', 'max:300')
                ->hideFromIndex(),

            Text::make(__('رقم العنوان الفرعي'), 'secondary_title_one_number')->rules('nullable', 'string', 'max:300'),

            Text::make(__('العنوان الفرعي'), 'secondary_title_one')
                ->rules('nullable', 'string', 'max:300')
                ->hideFromIndex(),

            Text::make(__('رقم العنوان الفرعي'), 'secondary_title_two_number')
                ->rules('nullable', 'integer', 'min:1', 'max:9999')
                ->step(1)
                ->min(1)
                ->max(9999)
                ->hideFromIndex(),

            Text::make(__('العنوان الفرعي'), 'secondary_title_two')
                ->rules('nullable', 'string', 'max:300')
                ->hideFromIndex(),

            Text::make(__('رقم الصفحة'), 'page_number')->rules('nullable', 'string', 'max:300'),

            Text::make(__('ترقيم المصدر'), 'source_number')
                ->rules('nullable', 'string', 'max:100')
                ->hideFromIndex(),

            Select::make(__('اللغة'), 'language_id')
                ->options([1 => 'ARABIC', 2 => 'ENGLISH', 3 => 'FRENCH'])
                ->rules('nullable', 'in:1,2,3')
                ->hideFromIndex(),

            Text::make(__('ملاحظات'), 'comments')
                ->rules('nullable', 'string', 'max:1000')
                ->hideFromIndex(),

            Hidden::make('user_id')
                ->default(Auth::user()->id),

            Boolean::make(__('مفعل'), 'is_valid')
                ->hideWhenCreating()
                ->hideWhenUpdating(),

            Boolean::make('خاص')
                ->hideFromDetail()
                ->hideWhenCreating()
                ->hideWhenUpdating()
                ->default(auth()->user()->id == $this->user_id)
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function cards(Request $request)
    {
        return [
            (new Validatedarticle($this->model()))->width('1/4'),
            (new Unvalidatedarticle($this->model()))->width('1/4'),
            (new Myvalidatedarticle($this->model()))->width('1/4'),
            (new Myunvalidatedarticle($this->model()))->width('1/4'),
        ];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function filters(Request $request)
    {
        return [
            new Filters\Inactivearticle('is_valid')
        ];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function actions(Request $request)
    {
        $exceptarray = ['deleted_at', 'user_id', 'is_valid', 'language_id', 'sourcegroup_id', 'updated_at', 'created_at', 'slug'];
        if (auth()->user()->role == 'validator') {
            $array = [
                (new ActivateArticle)->canSee(function (NovaRequest $request) {
                    return !$this->resource->is_valid && $this->resource->user_id != Auth::id();
                }),
                (new ExportxlsxResourceAction($this))->except($exceptarray)->usesGenerator(),
            ];
        } else {
            $array = [(new ExportxlsxResourceAction($this))->except($exceptarray)->usesGenerator()];
        }

        return $array;
    }
}