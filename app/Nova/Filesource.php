<?php

namespace App\Nova;


use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\Boolean;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\File;
use <PERSON><PERSON>\Nova\Fields\Textarea;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

class Filesource extends Resource
{
    public static function label()
    {
        return __('XLSX Files');
    }

    /**
     * Get the displayable singular label of the resource.
     *
     * @return string
     */
    public static function singularLabel()
    {
        return __('XLSX File');
    }

    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\Models\Filesource::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'title';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'title',
    ];


    public static $with = [
        'modelgroups',
    ];


    public function modelgroups(){
        return $this->belongsTo(\App\Models\Modelgroup::class);
    }


    public static function applySearch($query, $search)
    {
        $resultQurey =  $query->where('title', 'like', '%'.$search.'%')
                    ->orWhereHas('modelgroups', function ($query) use ($search) {
                        $query->where('title', 'like', '%'.$search.'%');
                    });
                  //  dd($resultQurey->toSql());
       return  $resultQurey;
    }

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function fields(Request $request)
    {
        return [
            ID::make(__('ID'), 'id')->sortable(),
            Text::make(__('العنوان'), 'title'),
            BelongsTo::make(__('Modelgroups'),'Modelgroups',Modelgroup::class),
            File::make(__('filename'), 'filename')
                ->disk('public')
                ->acceptedTypes('.csv, .xls, .xlsx')
                ->rules('required', 'file', 'mimes:csv,xls,xlsx', 'max:25600', function ($attribute, $value, $fail) {
                    if ($value) {
                        try {
                            \App\Services\FileValidationService::validateFile($value, [
                                'allowed_extensions' => ['csv', 'xls', 'xlsx'],
                                'allowed_mime_types' => [
                                    'text/csv',
                                    'application/csv',
                                    'application/vnd.ms-excel',
                                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                                ],
                                'max_size' => 25600 * 1024 // 25MB
                            ]);
                        } catch (\Illuminate\Validation\ValidationException $e) {
                            $fail($e->getMessage());
                        }
                    }
                }),


            Text::make(__('الوضعية'), function () {
                if($this->is_updated){
                    if($this->is_error){
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" aria-labelledby="x-circle" role="presentation" class="fill-current text-danger"><path d="M4.93 19.07A10 10 0 1 1 19.07 4.93 10 10 0 0 1 4.93 19.07zm1.41-1.41A8 8 0 1 0 17.66 6.34 8 8 0 0 0 6.34 17.66zM13.41 12l1.42 1.41a1 1 0 1 1-1.42 1.42L12 13.4l-1.41 1.42a1 1 0 1 1-1.42-1.42L10.6 12l-1.42-1.41a1 1 0 1 1 1.42-1.42L12 10.6l1.41-1.42a1 1 0 1 1 1.42 1.42L13.4 12z"></path></svg>';
                    }else{
                        return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" aria-labelledby="check-circle" role="presentation" class="fill-current text-success"><path d="M12 22a10 10 0 1 1 0-20 10 10 0 0 1 0 20zm0-2a8 8 0 1 0 0-16 8 8 0 0 0 0 16zm-2.3-8.7l1.3 1.29 3.3-3.3a1 1 0 0 1 1.4 1.42l-4 4a1 1 0 0 1-1.4 0l-2-2a1 1 0 0 1 1.4-1.42z"></path></svg>';
                    }
                }
                if($this->created_at->addMinutes(15) < now()){
                    return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" aria-labelledby="x-circle" role="presentation" class="fill-current text-danger"><path d="M4.93 19.07A10 10 0 1 1 19.07 4.93 10 10 0 0 1 4.93 19.07zm1.41-1.41A8 8 0 1 0 17.66 6.34 8 8 0 0 0 6.34 17.66zM13.41 12l1.42 1.41a1 1 0 1 1-1.42 1.42L12 13.4l-1.41 1.42a1 1 0 1 1-1.42-1.42L10.6 12l-1.42-1.41a1 1 0 1 1 1.42-1.42L12 10.6l1.41-1.42a1 1 0 1 1 1.42 1.42L13.4 12z"></path></svg>';
                }
                return __('جاري التحميل');
            })->asHtml(),
            Text::make(__('تاريخ التحميل'), 'created_at')->displayUsing(function($dat) {return $dat->Format('h:m:s d/m/Y ');})->onlyOnIndex(),


            text::make(__('ملاحضات'), 'comment')->hideWhenCreating()->hideWhenUpdating()->hideFromIndex()->asHtml(),

        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function filters(Request $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function actions(Request $request)
    {
        return [];
    }
}
