<?php

namespace App\Nova;

use App\Nova\Actions\ActivateArticle;
use App\Nova\Metrics\articles\Myunvalidatedarticle;
use App\Nova\Metrics\articles\Myvalidatedarticle;
use App\Nova\Metrics\articles\Unvalidatedarticle;
use App\Nova\Metrics\articles\Validatedarticle;
use Auth;
use Illuminate\Http\Request;
use Laravel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\Boolean;
use Laravel\Nova\Fields\Hidden;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Http\Requests\NovaRequest;
use Allanvb\NovaExports\ExportResourceAction;
use Allanvb\NovaExports\ExportxlsxResourceAction;
use Illuminate\Support\Str;

class InterConferenceRecommendation extends Resource
{
    public static function label()
    {
        return __('قرارات وتوصیات المؤتمرات الدولیة');
    }

    /**
     * Get the displayable singular label of the resource.
     *
     * @return string
     */
    public static function singularLabel()
    {
        return __('قرارات وتوصیات المؤتمرات الدولیة');
    }

    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\Models\InterConferenceRecommendation::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'title';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'title',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function fields(Request $request)
    {

        return [
            ID::make(__('#'), 'id')
                ->sortable()
                ->hideFromIndex(),

            Text::make(__('رقم'), 'code')
                ->rules('required', 'string', 'max:50', 'unique:inter_conference_recommendations,code,{{resourceId}}'),

            BelongsTo::make(__('عنوان المصدر'), 'sourcegroups', Sourcegroup::class)
                ->displayUsing(function ($dat) {
                    return Str::substr($dat->sourcetitle, 0, 250);
                })
                ->sortable()
                ->onlyOnIndex(),

            BelongsTo::make(__('عنوان المصدر'), 'sourcegroups', Sourcegroup::class)
                ->sortable()
                ->hideFromIndex()
                ->nullable()
                ->placeholder(__('اختر عنوان المصدر'))
                ->rules('nullable', 'exists:sourcegroups,id'),

            Text::make(__('الجهة المنظمة للمؤتمر'), 'author')->displayUsing(function ($id) {
                $part = strip_tags(substr($id, 0, 25));
                return $part . " ...";
            })
                ->onlyOnIndex(),

            Text::make(__('الجهة المنظمة للمؤتمر'), 'author')
                ->hideFromIndex(),

            Text::make(__('الرقم التسلسلي'), 'serial_number')
            ->hideFromIndex(),
            Text::make(__('رقم الدورة'), 'session_number')
                ->hideFromIndex(),

            Text::make(__('الرقم الفرعي'), 'secondary_number_one')
                ->hideFromIndex(),

            Text::make(__('الرقم الفرعي'), 'secondary_number_two')
                ->hideFromIndex(),

            Text::make(__('عنوان المؤتمر'), 'conference_title')
                ->displayUsing(function ($dat) {
                    return Str::substr($dat, 0, 25) . "...";
            })
                ->onlyOnIndex(),

            Text::make(__('عنوان المؤتمر'), 'conference_title')
                ->hideFromIndex(),

            Text::make(__('تاريخ الإصدار / الانعقاد'), 'year'),

            Text::make(__('عنوان القرار'), 'title')
                ->displayUsing(function ($dat) {
                    return Str::substr($dat, 0, 25) . "...";
            })
                ->onlyOnIndex(),

            Text::make(__('عنوان القرار'), 'title')
                ->hideFromIndex(),

            Text::make(__('أنشئت في'), 'created_at')
                ->displayUsing(function ($dat) {
                    if ($dat) {
                        return \Carbon\Carbon::parse($dat)->format('Y/m/d');
                    } else {
                        return null;
                    }
                })
                ->onlyOnIndex(),

            Text::make(__('رقم العنوان الفرعي'), 'secondary_title_one_number')
                ->hideFromIndex(),

            Text::make(__('العنوان الفرعي'), 'secondary_title_one')
                ->hideFromIndex(),

            Text::make(__('رقم العنوان الفرعي'), 'secondary_title_two_number')
                ->hideFromIndex(),

            Text::make(__(' العنوان الفرعي'), 'secondary_title_two')
                ->hideFromIndex(),

            Text::make(__('رقم الصفحة'), 'page_number')
                ->hideFromIndex(),

            Text::make(__('ترقيم المصدر'), 'source_number')
                ->hideFromIndex(),

            Select::make(__('اللغة'), 'language_id')
                ->options([1 => 'ARABIC', 2 => 'ENGLISH', 3 => 'FRENSH'])
                ->hideFromIndex(),

            Text::make(__('ملاحظات'), 'comments')
                ->hideFromIndex(),

            Hidden::make('user_id')
                ->default(Auth::user()->id),

            Boolean::make(__('مفعل'), 'is_valid')
                ->hideWhenCreating()->hideWhenUpdating(),

            Boolean::make('خاص')
                ->hideFromDetail()->hideWhenCreating()->hideWhenUpdating()->default(auth()->user()->id == $this->user_id)
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function cards(Request $request)
    {
        return [
            (new Validatedarticle($this->model()))->width('1/4'),
            (new Unvalidatedarticle($this->model()))->width('1/4'),
            (new Myvalidatedarticle($this->model()))->width('1/4'),
            (new Myunvalidatedarticle($this->model()))->width('1/4'),
        ];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function filters(Request $request)
    {
        return [
            new Filters\Inactivearticle('is_valid'),
            new Filters\Sourcenumber('source_number'),
            new Filters\DateCreated('created_at')

        ];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function actions(Request $request)
    {
        $exceptarray = ['deleted_at', 'user_id', 'is_valid', 'language_id', 'sourcegroup_id', 'updated_at', 'created_at', 'slug'];
        if (auth()->user()->role == 'validator') {
            $array = [
                (new ActivateArticle)->canSee(function (NovaRequest $request) {

                    return !$this->resource->is_valid && $this->resource->user_id != Auth::id();
                }),
                (new ExportxlsxResourceAction($this))->except($exceptarray)->usesGenerator(),
            ];
        } else
        $array = [(new ExportxlsxResourceAction($this))->except($exceptarray)->usesGenerator()];

        return $array;
    }
}
