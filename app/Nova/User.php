<?php

namespace App\Nova;

use Auth;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Image;
use <PERSON><PERSON>\Nova\Fields\Password;
use <PERSON>vel\Nova\Fields\Select;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use App\Services\XSSProtectionService;

class User extends Resource
{

    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\Models\User::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id', 'name', 'email',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function fields(Request $request)
    {
        return [
            ID::make()->sortable(),



            Text::make(__('الاسم'), 'name')
                ->sortable()
                ->rules('required', 'string', 'max:255', 'regex:/^[\p{L}\s\-\.]+$/u', function ($attribute, $value, $fail) {
                    // Check for XSS attempts
                    if (XSSProtectionService::detectXSS($value)) {
                        $fail(__('الرجاء إدخال اسم صحيح بدون رموز خطيرة.'));
                        return;
                    }

                    // Basic name validation
                    if (strlen(trim($value)) < 2) {
                        $fail(__('الرجاء إدخال اسم يحتوي على حرفين على الأقل.'));
                        return;
                    }
                }),

            Text::make('البريد الالكتروني','email')
                ->sortable()
                ->rules('required', 'email:rfc,dns', 'max:254', function ($attribute, $value, $fail) {
                    // Check for XSS attempts
                    if (XSSProtectionService::detectXSS($value)) {
                        $fail(__('الرجاء إدخال بريد إلكتروني صحيح.'));
                        return;
                    }

                    // Additional email security checks
                    $suspiciousDomains = ['tempmail.org', '10minutemail.com', 'guerrillamail.com'];
                    $domain = substr(strrchr($value, "@"), 1);
                    if (in_array($domain, $suspiciousDomains)) {
                        $fail(__('الرجاء استخدام بريد إلكتروني دائم وليس مؤقت.'));
                        return;
                    }
                })
                ->creationRules('unique:users,email')
                ->updateRules('unique:users,email,{{resourceId}}'),

            Image::make(__('الصورة'),'avatar')
                ->maxWidth(150)
                ->disableDownload()
                ->acceptedTypes('.png,.jpg,.jpeg,.webp')
                ->rules('mimes:png,jpg,jpeg,webp', 'max:5120', function ($attribute, $value, $fail) {
                    if ($value) {
                        try {
                            \App\Services\FileValidationService::validateFile($value, [
                                'allowed_extensions' => ['png', 'jpg', 'jpeg', 'webp'],
                                'allowed_mime_types' => ['image/png', 'image/jpg', 'image/jpeg', 'image/webp'],
                                'max_size' => 5120 * 1024 // 5MB
                            ]);
                        } catch (\Illuminate\Validation\ValidationException $e) {
                            $fail($e->getMessage());
                        }
                    }
                }),
            Password::make('كلمة السر','password')
                ->onlyOnForms()
                ->rules('required')
                ->creationRules('required', 'string', 'min:8', 'regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/')
                ->updateRules('nullable', 'string', 'min:8', 'regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/'),
            Select::make(__('الوظيفة'),'role')->options(['validator'=>'Validator','user'=>'User','superadmin'=>'SuperAdmin'])->default('validator')
            ->canSee(
                function($fn){
                    if(Auth::user()->role ==='superadmin')
                    return true;
                    else
                    return false;
                }
                //Auth::user()->role ==='superadmin'
                )
        ];
    }
        /**
     * Build an "index" query for the given resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public static function indexQuery(NovaRequest $request, $query)
    {
        if(auth()->user()->role != 'developer')
        return $query->where('role','!=','developer');
        else
        return $query;
    }


    /**
     * Get the cards available for the request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function filters(Request $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function actions(Request $request)
    {
        return [];
    }
}
