<?php

namespace Lara<PERSON>\Nova\Trix;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use <PERSON>vel\Nova\Fields\Trix;

class StorePendingAttachment
{
    /**
     * The field instance.
     *
     * @var \Laravel\Nova\Fields\Trix
     */
    public $field;

    /**
     * Create a new invokable instance.
     *
     * @param  \Laravel\Nova\Fields\Trix  $field
     * @return void
     */
    public function __construct(Trix $field)
    {
        $this->field = $field;
    }

    /**
     * Attach a pending attachment to the field.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return string
     */
    public function __invoke(Request $request)
    {
        // Add server-side file validation
        $this->validateFileUpload($request);

        $disk = $this->field->getStorageDisk();
        $file = $request->file('attachment');

        // Generate a new, random, and safe filename
        $extension = $file->getClientOriginalExtension();
        $safeFilename = \Illuminate\Support\Str::uuid() . '.' . $extension;

        return Storage::disk($disk)->url(PendingAttachment::create([
            'draft_id' => $request->draftId,
            'attachment' => $file->storeAs($this->field->getStorageDir(), $safeFilename, $disk),
            'disk' => $disk,
        ])->attachment);
    }

    /**
     * Validate the uploaded file for security.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return void
     */
    protected function validateFileUpload(Request $request)
    {
        $allowedMimes = [
            'jpg', 'jpeg', 'png', 'gif', 'webp', 'svg',
            'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx',
            'txt', 'rtf', 'csv', 'zip', 'rar', '7z',
            'mp4', 'avi', 'mov', 'wmv', 'flv', 'webm',
            'mp3', 'wav', 'ogg', 'aac', 'flac'
        ];

        $maxFileSize = 10240; // 10MB in KB

        $validator = Validator::make($request->all(), [
            'attachment' => [
                'required',
                'file',
                'mimes:' . implode(',', $allowedMimes),
                'max:' . $maxFileSize,
                function ($attribute, $value, $fail) {
                    if ($value) {
                        // Check for executable file extensions
                        $dangerousExtensions = [
                            'php', 'php3', 'php4', 'php5', 'phtml', 'phps',
                            'js', 'exe', 'bat', 'cmd', 'com', 'pif', 'scr',
                            'vbs', 'vbe', 'jse', 'ws', 'wsf', 'wsc', 'wsh',
                            'ps1', 'ps1xml', 'ps2', 'ps2xml', 'psc1', 'psc2',
                            'msh', 'msh1', 'msh2', 'mshxml', 'msh1xml', 'msh2xml',
                            'scf', 'lnk', 'inf', 'reg', 'asp', 'aspx', 'jsp',
                            'jar', 'war', 'ear', 'class', 'py', 'rb', 'pl',
                            'sh', 'bash', 'zsh', 'fish', 'csh', 'tcsh'
                        ];

                        $extension = strtolower($value->getClientOriginalExtension());
                        if (in_array($extension, $dangerousExtensions)) {
                            $fail('The uploaded file type is not allowed for security reasons.');
                        }

                        // Check MIME type against file extension
                        $realMimeType = $value->getMimeType();
                        $allowedMimeTypes = [
                            'image/jpeg', 'image/jpg', 'image/png', 'image/gif',
                            'image/webp', 'image/svg+xml',
                            'application/pdf',
                            'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                            'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                            'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                            'text/plain', 'text/rtf', 'text/csv',
                            'application/zip', 'application/x-rar-compressed', 'application/x-7z-compressed',
                            'video/mp4', 'video/x-msvideo', 'video/quicktime', 'video/x-ms-wmv', 'video/x-flv', 'video/webm',
                            'audio/mpeg', 'audio/wav', 'audio/ogg', 'audio/aac', 'audio/flac'
                        ];

                        if (!in_array($realMimeType, $allowedMimeTypes)) {
                            $fail('The uploaded file MIME type is not allowed.');
                        }
                    }
                }
            ]
        ]);

        if ($validator->fails()) {
            throw new \Illuminate\Validation\ValidationException($validator);
        }
    }
}
